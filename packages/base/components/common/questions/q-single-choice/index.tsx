import { computed, defineComponent } from 'vue'
import type { PropType } from 'vue'
import { useQuestionsForm } from '@sa/hooks'
import type { TransformToVoQuestionData } from '@sa/utils'
import CKEditor from '@sa/components/common/ck-editor/index.vue'
import styles from './index.module.css'

export default defineComponent({
  name: 'SingleChoice',
  props: {
    item: {
      type: Object as PropType<TransformToVoQuestionData>,
      required: true,
    },
    modelValue: {
      type: [String, Number],
      default: '',
    },
    type: {
      type: String as PropType<'edit' | 'answer' | 'preview'>,
      default: 'answer',
    },

  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    const modelValue = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const isdisabled = computed(() => props.type === 'preview')
    const { mergedDisabled } = useQuestionsForm({
      type: computed(() => props.type),
    })

    const handleChange = (value: any) => {
      if (mergedDisabled.value)
        return
      modelValue.value = value
    }

    return () => {
      return (
        <ul class={styles.singleChoice}>
          {props.item.options?.map(option => (
            <li key={option.value}>
              <label class={styles.choiceItem}>
                <div
                  class={[
                    styles.choiceItemQn,
                    !isdisabled.value && styles.cursor,
                    !isdisabled.value && props.item.correctAnswer === option.value ? styles.choiceItemQnChecked : '',
                  ]}
                  onClick={() => {
                    if (mergedDisabled.value) {
                      return
                    }
                    handleChange(option.value)
                  }}
                >
                  <span>{option.value}</span>
                </div>
                {
                  !isdisabled.value && (
                    <CKEditor
                      v-model:editorValue={option.label}
                      minHeight={32}
                    />
                  )
                }
                {
                  isdisabled.value && (
                    <span
                      class={[styles.choiceItemLabel, 'contents']}
                      v-html={option.label}
                      v-katex
                    />
                  )
                }
              </label>
            </li>
          ))}
        </ul>
      )
    }
  },
})
